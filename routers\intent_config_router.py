"""
意图识别配置管理路由
支持动态配置管理、热更新和配置验证
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel, Field

from api.intent_config_api import intent_config_api
from model.response_models import BaseResponse

# 创建路由器
router = APIRouter(prefix="/api/v1/intent-config", tags=["意图识别配置管理"])


class IntentTypeRequest(BaseModel):
    """意图类型请求模型"""
    intent_type: str = Field(..., description="意图类型标识")
    display_name: str = Field(..., description="显示名称")
    priority: int = Field(default=50, description="优先级")
    category: str = Field(default="custom", description="分类")


class PromptUpdateRequest(BaseModel):
    """提示词更新请求模型"""
    prompt_type: str = Field(..., description="提示词类型")
    prompt_content: str = Field(..., description="提示词内容")


class SafetyConfigRequest(BaseModel):
    """安全配置请求模型"""
    risk_keywords: List[str] = Field(default=[], description="风险关键词")
    educational_patterns: List[str] = Field(default=[], description="教育模式")
    instructive_patterns: List[str] = Field(default=[], description="指导模式")
    custom_safety_rules: Dict[str, Any] = Field(default={}, description="自定义安全规则")


@router.get("/current", summary="获取当前配置")
async def get_current_config():
    """获取当前意图识别配置"""
    return await intent_config_api.get_current_config()


@router.post("/update", summary="更新配置")
async def update_config(updates: Dict[str, Any] = Body(...)):
    """更新意图识别配置"""
    return await intent_config_api.update_config(updates)


@router.post("/reload", summary="重新加载配置")
async def reload_config():
    """重新加载配置文件"""
    return await intent_config_api.reload_config()


@router.get("/intent-types", summary="获取意图类型")
async def get_intent_types():
    """获取所有意图类型配置"""
    return await intent_config_api.get_intent_types()


@router.post("/intent-types", summary="添加意图类型")
async def add_intent_type(request: IntentTypeRequest):
    """添加新的意图类型"""
    return await intent_config_api.add_intent_type(
        intent_type=request.intent_type,
        display_name=request.display_name,
        priority=request.priority,
        category=request.category
    )


@router.delete("/intent-types/{intent_type}", summary="删除意图类型")
async def remove_intent_type(intent_type: str):
    """删除指定的意图类型"""
    return await intent_config_api.remove_intent_type(intent_type)


@router.get("/prompts", summary="获取提示词")
async def get_prompts():
    """获取所有提示词配置"""
    return await intent_config_api.get_prompts()


@router.post("/prompts", summary="更新提示词")
async def update_prompt(request: PromptUpdateRequest):
    """更新指定的提示词"""
    return await intent_config_api.update_prompt(
        prompt_type=request.prompt_type,
        prompt_content=request.prompt_content
    )


@router.get("/safety", summary="获取安全配置")
async def get_safety_config():
    """获取安全检查配置"""
    return await intent_config_api.get_safety_config()


@router.post("/safety", summary="更新安全配置")
async def update_safety_config(request: SafetyConfigRequest):
    """更新安全检查配置"""
    safety_updates = {}
    if request.risk_keywords:
        safety_updates["risk_keywords"] = request.risk_keywords
    if request.educational_patterns:
        safety_updates["educational_patterns"] = request.educational_patterns
    if request.instructive_patterns:
        safety_updates["instructive_patterns"] = request.instructive_patterns
    if request.custom_safety_rules:
        safety_updates["custom_safety_rules"] = request.custom_safety_rules
    
    return await intent_config_api.update_safety_config(safety_updates)


@router.post("/validate", summary="验证配置")
async def validate_config(config_data: Dict[str, Any] = Body(...)):
    """验证配置数据的有效性"""
    return await intent_config_api.validate_config(config_data)


@router.get("/export", summary="导出配置")
async def export_config():
    """导出当前配置为JSON格式"""
    return await intent_config_api.export_config()


@router.post("/import", summary="导入配置")
async def import_config(config_data: Dict[str, Any] = Body(...)):
    """从JSON数据导入配置"""
    return await intent_config_api.import_config(config_data)


@router.get("/templates", summary="获取配置模板")
async def get_config_templates():
    """获取配置模板和示例"""
    return {
        "success": True,
        "data": {
            "intent_type_template": {
                "intent_type": "custom_intent",
                "display_name": "自定义意图",
                "priority": 50,
                "category": "custom"
            },
            "prompt_template": {
                "prompt_type": "custom_prompt",
                "prompt_content": "你是一个专业的助手，请分析以下查询：\n\n查询内容：\"{query}\"\n\n请提供详细的分析和建议。"
            },
            "safety_rule_template": {
                "rule_name": "custom_rule",
                "keywords": ["关键词1", "关键词2"],
                "patterns": ["正则表达式1", "正则表达式2"],
                "action": "flag_as_suspicious",
                "message": "检测到可疑内容"
            }
        },
        "message": "获取配置模板成功"
    }


@router.get("/status", summary="获取配置状态")
async def get_config_status():
    """获取配置管理器状态"""
    try:
        config = await intent_config_api.get_current_config()
        return {
            "success": True,
            "data": {
                "config_loaded": True,
                "config_path": config.get("data", {}).get("config_path"),
                "last_updated": config.get("timestamp"),
                "total_intent_types": len(config.get("data", {}).get("intent_types", {}).get("intent_types", {})) + 
                                    len(config.get("data", {}).get("intent_types", {}).get("custom_intent_types", {})),
                "total_prompts": len(config.get("data", {}).get("llm_prompts", {}).get("custom_prompts", {})) + 3,  # 3个默认提示词
                "safety_keywords_count": len(config.get("data", {}).get("safety", {}).get("risk_keywords", [])),
                "custom_safety_rules_count": len(config.get("data", {}).get("safety", {}).get("custom_safety_rules", {}))
            },
            "message": "获取配置状态成功"
        }
    except Exception as e:
        return {
            "success": False,
            "data": {
                "config_loaded": False,
                "error": str(e)
            },
            "message": "获取配置状态失败"
        }


# 配置示例端点
@router.get("/examples", summary="获取配置示例")
async def get_config_examples():
    """获取各种配置的示例"""
    return {
        "success": True,
        "data": {
            "complete_config_example": {
                "base": {
                    "confidence_threshold": 0.8,
                    "enable_llm": True,
                    "enable_dfa_filter": True,
                    "enable_query_enhancement": True
                },
                "intent_types": {
                    "custom_intent_types": {
                        "product_inquiry": "产品咨询",
                        "technical_issue": "技术问题"
                    },
                    "intent_priorities": {
                        "product_inquiry": 80,
                        "technical_issue": 90
                    }
                },
                "llm_prompts": {
                    "custom_prompts": {
                        "product_analysis": "请分析以下产品相关查询：\"{query}\""
                    }
                },
                "safety": {
                    "risk_keywords": ["新增风险词"],
                    "custom_safety_rules": {
                        "product_safety": {
                            "keywords": ["产品安全", "质量问题"],
                            "action": "flag_for_review"
                        }
                    }
                }
            },
            "prompt_examples": {
                "business_analysis": "你是一个商业分析专家，请分析以下查询的商业价值：\n\n查询：\"{query}\"\n\n请从市场、竞争、风险等角度进行分析。",
                "technical_support": "你是一个技术支持专家，请为以下技术问题提供解决方案：\n\n问题：\"{query}\"\n\n请提供诊断步骤和解决方案。"
            },
            "safety_rule_examples": {
                "financial_risk": {
                    "keywords": ["投资", "理财", "高收益"],
                    "patterns": ["保证收益\\d+%", "无风险投资"],
                    "action": "flag_as_suspicious",
                    "message": "检测到可能的金融风险内容"
                },
                "privacy_protection": {
                    "keywords": ["身份证", "银行卡", "密码"],
                    "patterns": ["\\d{15,18}", "\\d{16,19}"],
                    "action": "mask_sensitive_info",
                    "message": "检测到敏感个人信息"
                }
            }
        },
        "message": "获取配置示例成功"
    }


# 导出路由器
__all__ = ["router"]
