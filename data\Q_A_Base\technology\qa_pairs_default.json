{"metadata": {"version": "1.0", "created_at": 1755540861.7271316, "total_pairs": 9}, "qa_pairs": [{"id": "qa_0cd75a61", "question": "什么是人工智能？", "answer": "人工智能（Artificial Intelligence，AI）是指由机器展现出的智能行为，包括学习、推理、感知、理解、交流和在复杂环境中行动的能力。", "category": "technology", "confidence": 0.95, "keywords": ["人工智能", "AI", "机器学习", "智能"], "source": "技术百科", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_81ba75a6", "question": "机器学习和深度学习有什么区别？", "answer": "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习。深度学习是机器学习的一个子集，使用多层神经网络来模拟人脑的学习过程，能够处理更复杂的数据和任务。", "category": "technology", "confidence": 0.9, "keywords": ["机器学习", "深度学习", "神经网络", "算法"], "source": "技术文档", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_ebdbc417", "question": "什么是云计算？", "answer": "云计算是一种通过互联网提供计算服务的模式，包括服务器、存储、数据库、网络、软件等资源。用户可以按需使用这些资源，无需购买和维护物理硬件。", "category": "technology", "confidence": 0.9, "keywords": ["云计算", "互联网", "服务器", "存储"], "source": "技术百科", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_fa5b7528", "question": "数据库的作用是什么？", "answer": "数据库是用于存储、管理和检索数据的系统。它提供了数据的持久化存储、数据完整性保证、并发访问控制、数据安全性等功能，是现代应用系统的重要组成部分。", "category": "technology", "confidence": 0.88, "keywords": ["数据库", "存储", "数据管理", "SQL"], "source": "技术文档", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_c034a963", "question": "什么是API？", "answer": "API（Application Programming Interface，应用程序编程接口）是一组定义了软件组件之间如何交互的规则和协议。它允许不同的应用程序或服务之间进行通信和数据交换。", "category": "technology", "confidence": 0.92, "keywords": ["API", "接口", "编程", "通信"], "source": "技术文档", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_a2813663", "question": "什么是区块链？", "answer": "区块链是一种分布式账本技术，通过密码学方法将数据块按时间顺序链接起来，形成不可篡改的数据链。它具有去中心化、透明性、不可篡改等特点，广泛应用于加密货币、供应链管理等领域。", "category": "technology", "confidence": 0.9, "keywords": ["区块链", "分布式", "加密", "去中心化"], "source": "技术百科", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_90193397", "question": "什么是敏捷开发？", "answer": "敏捷开发是一种软件开发方法论，强调个体和互动胜过流程和工具、工作的软件胜过详尽的文档、客户合作胜过合同谈判、响应变化胜过遵循计划。它通过迭代和增量的方式快速交付价值。", "category": "technology", "confidence": 0.88, "keywords": ["敏捷开发", "软件开发", "迭代", "团队协作"], "source": "开发方法论", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_704259c7", "question": "什么是物联网？", "answer": "物联网（Internet of Things，IoT）是指通过互联网连接的物理设备网络，这些设备可以收集和交换数据。包括智能家居设备、可穿戴设备、工业传感器等，实现设备间的智能互联。", "category": "technology", "confidence": 0.9, "keywords": ["物联网", "IoT", "智能设备", "传感器"], "source": "技术百科", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}, {"id": "qa_2eea7fdd", "question": "什么是DevOps？", "answer": "DevOps是开发（Development）和运维（Operations）的结合，是一种软件开发和IT运维的协作文化和实践。它通过自动化、持续集成、持续部署等方式，提高软件交付的速度和质量。", "category": "technology", "confidence": 0.87, "keywords": ["DevOps", "开发", "运维", "自动化", "持续集成"], "source": "技术文档", "created_at": 1755525138.1735048, "updated_at": 1755525138.1735048}]}