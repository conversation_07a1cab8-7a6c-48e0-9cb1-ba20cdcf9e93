# GuiXiaoXiRag FastAPI 环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# ==================== 应用基础配置 ====================
APP_NAME=GuiXiaoXiRag FastAPI Service
APP_VERSION=0.1.0

# ==================== 服务配置 ====================
HOST=0.0.0.0
PORT=8002
DEBUG=false
WORKERS=1

# ==================== 知识库配置 ====================
# 数据目录
DATA_DIR=./data
# 知识库工作目录
WORKING_DIR=./data/knowledgeBase/default
# 问答系统存储目录
QA_STORAGE_DIR=./data/Q_A_Base

# ==================== LLM 服务配置 ====================
# LLM API 配置（聊天模型）
OPENAI_API_BASE=http://localhost:8100/v1
OPENAI_CHAT_API_KEY=sk-your-llm-api-key-here
OPENAI_CHAT_MODEL=qwen14b

# Embedding API 配置（向量模型）
OPENAI_EMBEDDING_API_BASE=http://localhost:8200/v1
OPENAI_EMBEDDING_API_KEY=sk-your-embedding-api-key-here
OPENAI_EMBEDDING_MODEL=embedding_qwen

# 如果使用官方 OpenAI API，请修改为：
# OPENAI_API_BASE=https://api.openai.com/v1
# OPENAI_EMBEDDING_API_BASE=https://api.openai.com/v1
# OPENAI_CHAT_API_KEY=your-openai-api-key-here
# OPENAI_CHAT_MODEL=gpt-4o-mini
# OPENAI_EMBEDDING_MODEL=text-embedding-3-large
# OPENAI_EMBEDDING_API_KEY=your-openai-api-key-here

# ==================== 模型参数配置 ====================
# 向量维度配置
EMBEDDING_DIM=2560
MAX_TOKEN_SIZE=8192

# 模型超时配置（秒）
LLM_TIMEOUT=240
EMBEDDING_TIMEOUT=240

# ==================== 其他 LLM 提供商配置 (可选) ====================
# Azure OpenAI 配置
# AZURE_API_VERSION=2024-02-15-preview
# AZURE_DEPLOYMENT_NAME=your-deployment-name

# Ollama 本地配置
# OLLAMA_BASE_URL=http://localhost:11434
# OLLAMA_CHAT_MODEL=llama2
# OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# ==================== 文件处理配置 ====================
# 文件大小限制 (50MB)
MAX_FILE_SIZE=52428800
# 上传目录
UPLOAD_DIR=./uploads

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_DIR=./logs

# ==================== 性能配置 ====================
# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600

# 并发配置
MAX_CONCURRENT_REQUESTS=100

# ==================== 网关协同配置 ====================
# 与 Java 网关的协同配置，支持用户优先限流
ENABLE_PROXY_HEADERS=true
TRUSTED_PROXY_IPS=["10.0.0.0/8","************"]

# 用户标识头部配置
USER_ID_HEADER=x-user-id
CLIENT_ID_HEADER=x-client-id
USER_TIER_HEADER=x-user-tier

# 分层限流配置（每分钟请求上限）
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_TIERS={"default":100,"free":60,"pro":600,"enterprise":3000}
RATE_LIMIT_DEFAULT_TIER=default
MIN_INTERVAL_PER_USER=0.5

# ==================== 安全配置 ====================
# CORS 配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# ==================== 可选配置 ====================
# 以下配置项为可选，根据需要启用

# Streamlit 界面配置（如果需要Web界面）
# STREAMLIT_HOST=0.0.0.0
# STREAMLIT_PORT=8501
# STREAMLIT_API_URL=http://localhost:8002

# 开发调试配置
# DEV_MODE=false
# METRICS_ENABLED=true
# HEALTH_CHECK_INTERVAL=30
