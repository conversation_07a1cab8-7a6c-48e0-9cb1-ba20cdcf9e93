#!/usr/bin/env python3
"""
测试意图识别配置系统
"""
import asyncio
import json
import sys
import os
from pathlib import Path

def test_config_system():
    """测试配置系统"""
    print("=== 测试意图识别配置系统 ===")
    
    try:
        # 1. 测试配置管理器导入
        print("1. 测试配置管理器导入...")
        try:
            from core.intent_recognition.config_manager import get_config_manager, get_processor_config
            print("✅ 配置管理器导入成功")
        except Exception as e:
            print(f"❌ 配置管理器导入失败: {e}")
            return False
        
        # 2. 测试配置加载
        print("\n2. 测试配置加载...")
        try:
            config_manager = get_config_manager()
            config = config_manager.get_config()
            print("✅ 配置加载成功")
            print(f"   配置路径: {config.config_path}")
            print(f"   启用LLM: {config.enable_llm}")
            print(f"   置信度阈值: {config.confidence_threshold}")
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return False
        
        # 3. 测试意图类型配置
        print("\n3. 测试意图类型配置...")
        try:
            intent_types = config.intent_type_config.intent_types
            custom_intents = config.intent_type_config.custom_intent_types
            print(f"✅ 内置意图类型: {len(intent_types)} 个")
            print(f"✅ 自定义意图类型: {len(custom_intents)} 个")
            
            # 显示前几个意图类型
            for i, (intent_type, display_name) in enumerate(list(intent_types.items())[:3]):
                print(f"   - {intent_type}: {display_name}")
            
        except Exception as e:
            print(f"❌ 意图类型配置测试失败: {e}")
        
        # 4. 测试安全配置
        print("\n4. 测试安全配置...")
        try:
            safety_config = config.safety_config
            print(f"✅ 安全级别: {len(safety_config.safety_levels)} 个")
            print(f"✅ 风险关键词: {len(safety_config.risk_keywords)} 个")
            print(f"✅ 教育模式: {len(safety_config.educational_patterns)} 个")
            print(f"✅ 指导模式: {len(safety_config.instructive_patterns)} 个")
        except Exception as e:
            print(f"❌ 安全配置测试失败: {e}")
        
        # 5. 测试提示词配置
        print("\n5. 测试提示词配置...")
        try:
            prompt_config = config.llm_prompt_config
            custom_prompts = prompt_config.custom_prompts or {}
            print(f"✅ 自定义提示词: {len(custom_prompts)} 个")
            
            # 显示自定义提示词
            for prompt_type in list(custom_prompts.keys())[:2]:
                print(f"   - {prompt_type}")
                
        except Exception as e:
            print(f"❌ 提示词配置测试失败: {e}")
        
        # 6. 测试配置文件
        print("\n6. 测试配置文件...")
        try:
            config_file = "./data/custom_intents/processor_config.json"
            if os.path.exists(config_file):
                print(f"✅ 配置文件存在: {config_file}")
                
                # 读取并验证配置文件
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                required_sections = ['base', 'llm_prompts', 'intent_types', 'safety']
                for section in required_sections:
                    if section in config_data:
                        print(f"   ✅ 配置节存在: {section}")
                    else:
                        print(f"   ❌ 配置节缺失: {section}")
                        
            else:
                print(f"⚠️  配置文件不存在: {config_file}")
                
        except Exception as e:
            print(f"❌ 配置文件测试失败: {e}")
        
        # 7. 测试处理器初始化
        print("\n7. 测试处理器初始化...")
        try:
            from core.intent_recognition.processor import IntentRecognitionProcessor
            processor = IntentRecognitionProcessor()
            print("✅ 处理器初始化成功")
            
            # 测试获取可用意图类型
            available_intents = processor.get_available_intent_types()
            print(f"✅ 可用意图类型: {len(available_intents)} 个")
            
            # 测试获取安全级别
            safety_levels = processor.get_available_safety_levels()
            print(f"✅ 可用安全级别: {len(safety_levels)} 个")
            
        except Exception as e:
            print(f"❌ 处理器初始化失败: {e}")
        
        # 8. 测试配置更新
        print("\n8. 测试配置更新...")
        try:
            # 添加一个测试意图类型
            processor.add_custom_intent_type("test_intent", "测试意图", 60)
            print("✅ 添加自定义意图类型成功")
            
            # 验证是否添加成功
            updated_intents = processor.get_available_intent_types()
            if "test_intent" in updated_intents:
                print("✅ 自定义意图类型验证成功")
            else:
                print("❌ 自定义意图类型验证失败")
            
            # 移除测试意图类型
            processor.remove_custom_intent_type("test_intent")
            print("✅ 移除自定义意图类型成功")
            
        except Exception as e:
            print(f"❌ 配置更新测试失败: {e}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def test_api_integration():
    """测试API集成"""
    print("\n=== 测试API集成 ===")
    
    try:
        # 测试API导入
        print("1. 测试API导入...")
        try:
            from api.intent_config_api import intent_config_api
            print("✅ API导入成功")
        except Exception as e:
            print(f"❌ API导入失败: {e}")
            return False
        
        # 测试获取当前配置
        print("\n2. 测试获取当前配置...")
        try:
            result = await intent_config_api.get_current_config()
            if result.get("success"):
                print("✅ 获取当前配置成功")
                config_data = result.get("data", {})
                print(f"   配置节数量: {len(config_data)}")
            else:
                print(f"❌ 获取当前配置失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取当前配置异常: {e}")
        
        # 测试获取意图类型
        print("\n3. 测试获取意图类型...")
        try:
            result = await intent_config_api.get_intent_types()
            if result.get("success"):
                print("✅ 获取意图类型成功")
                data = result.get("data", {})
                intent_types = data.get("intent_types", {})
                custom_intents = data.get("custom_intent_types", {})
                print(f"   内置意图: {len(intent_types)} 个")
                print(f"   自定义意图: {len(custom_intents)} 个")
            else:
                print(f"❌ 获取意图类型失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取意图类型异常: {e}")
        
        # 测试获取提示词
        print("\n4. 测试获取提示词...")
        try:
            result = await intent_config_api.get_prompts()
            if result.get("success"):
                print("✅ 获取提示词成功")
                data = result.get("data", {})
                custom_prompts = data.get("custom_prompts", {})
                print(f"   自定义提示词: {len(custom_prompts)} 个")
            else:
                print(f"❌ 获取提示词失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取提示词异常: {e}")
        
        # 测试获取安全配置
        print("\n5. 测试获取安全配置...")
        try:
            result = await intent_config_api.get_safety_config()
            if result.get("success"):
                print("✅ 获取安全配置成功")
                data = result.get("data", {})
                risk_keywords = data.get("risk_keywords", [])
                safety_levels = data.get("safety_levels", {})
                print(f"   风险关键词: {len(risk_keywords)} 个")
                print(f"   安全级别: {len(safety_levels)} 个")
            else:
                print(f"❌ 获取安全配置失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取安全配置异常: {e}")
        
        print("\n=== API集成测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ API集成测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("开始测试意图识别配置系统...")
    
    # 测试基础配置系统
    config_success = test_config_system()
    
    # 测试API集成
    api_success = asyncio.run(test_api_integration())
    
    # 总结
    print("\n" + "="*50)
    print("测试总结:")
    print(f"配置系统测试: {'✅ 通过' if config_success else '❌ 失败'}")
    print(f"API集成测试: {'✅ 通过' if api_success else '❌ 失败'}")
    
    if config_success and api_success:
        print("\n🎉 所有测试通过！意图识别配置系统工作正常。")
        print("\n📝 使用说明:")
        print("1. 配置文件位置: ./data/custom_intents/processor_config.json")
        print("2. API端点: /intent-config/*")
        print("3. 支持热更新和动态配置")
        print("4. 可通过API或直接修改配置文件进行配置")
        return True
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
