{"base": {"confidence_threshold": 0.7, "enable_llm": true, "enable_dfa_filter": true, "enable_query_enhancement": true, "sensitive_vocabulary_path": "core/intent_recognition/sensitive_vocabulary"}, "llm_prompts": {"safety_check_prompt": null, "intent_analysis_prompt": null, "query_enhancement_prompt": null, "custom_prompts": {"business_analysis": "你是一个商业分析专家，请分析以下查询的商业价值和市场意义：\n\n查询内容：\"{query}\"\n\n请从以下角度分析：\n1. 市场需求分析\n2. 商业价值评估\n3. 竞争优势分析\n4. 风险评估\n\n请以JSON格式返回分析结果。", "technical_support": "你是一个技术支持专家，请为以下技术问题提供解决方案：\n\n问题描述：\"{query}\"\n\n请提供：\n1. 问题诊断\n2. 解决步骤\n3. 预防措施\n4. 相关资源\n\n请以JSON格式返回结果。"}}, "intent_types": {"intent_types": {"knowledge_query": "知识查询", "factual_question": "事实性问题", "analytical_question": "分析性问题", "procedural_question": "程序性问题", "creative_request": "创意请求", "greeting": "问候", "unclear": "意图不明确", "illegal_content": "非法内容"}, "custom_intent_types": {"business_inquiry": "商业咨询", "technical_support": "技术支持", "product_recommendation": "产品推荐", "complaint_handling": "投诉处理", "service_consultation": "服务咨询"}, "intent_priorities": {"illegal_content": 100, "complaint_handling": 95, "technical_support": 90, "business_inquiry": 85, "product_recommendation": 80, "service_consultation": 75, "knowledge_query": 70, "factual_question": 65, "analytical_question": 60, "procedural_question": 55, "creative_request": 50, "greeting": 45, "unclear": 10}, "intent_categories": {"knowledge_query": "information", "factual_question": "information", "analytical_question": "analysis", "procedural_question": "instruction", "creative_request": "creative", "greeting": "social", "unclear": "unknown", "illegal_content": "security", "business_inquiry": "business", "technical_support": "support", "product_recommendation": "sales", "complaint_handling": "service", "service_consultation": "service"}}, "safety": {"safety_levels": {"safe": "安全", "suspicious": "可疑", "unsafe": "不安全", "illegal": "非法"}, "risk_keywords": ["赌博", "毒品", "色情", "暴力", "诈骗", "非法", "违法", "恐怖主义", "gambling", "drugs", "pornography", "violence", "fraud", "illegal", "terrorism", "洗钱", "贩毒", "走私", "绑架", "勒索", "黑客", "病毒", "木马", "money laundering", "drug trafficking", "smuggling", "kidnapping", "extortion", "hacker", "virus", "trojan"], "educational_patterns": ["如何防范", "如何识别", "如何举报", "防范措施", "安全知识", "预防方法", "how to prevent", "how to identify", "how to report", "safety measures", "prevention methods", "安全教育", "风险提示", "警示案例", "法律知识", "合规要求", "safety education", "risk warning", "case study", "legal knowledge", "compliance requirements"], "instructive_patterns": ["如何实施", "如何制作", "如何购买", "制作方法", "购买渠道", "操作步骤", "how to implement", "how to make", "how to buy", "manufacturing method", "purchase channel", "详细教程", "具体步骤", "实操指南", "技术细节", "工具使用", "detailed tutorial", "specific steps", "practical guide", "technical details", "tool usage"], "custom_safety_rules": {"financial_fraud_detection": {"keywords": ["投资理财", "高收益", "无风险", "快速致富", "内幕消息"], "patterns": ["保证收益\\d+%", "日赚\\d+元", "躺赚", "一夜暴富"], "action": "flag_as_suspicious", "message": "检测到可能的金融诈骗内容"}, "personal_info_protection": {"keywords": ["身份证", "银行卡", "密码", "验证码", "个人信息"], "patterns": ["\\d{15,18}", "\\d{16,19}", "\\d{6}"], "action": "mask_sensitive_info", "message": "检测到敏感个人信息"}}}, "enhancement_templates": {"knowledge_query": ["请详细解释{query}的概念、特点和应用场景", "关于{query}，请提供全面的背景信息和相关知识", "请从多个角度分析{query}的重要性和影响"], "factual_question": ["请提供关于{query}的准确事实信息和数据", "关于{query}，请给出具体的时间、地点、人物等详细信息", "请列出与{query}相关的关键事实和统计数据"], "analytical_question": ["请深入分析{query}，包括原因、影响和解决方案", "关于{query}，请提供多角度的分析和见解", "请系统性地分析{query}的各个方面和相互关系"], "procedural_question": ["请提供{query}的详细步骤和操作指南", "关于{query}，请给出清晰的流程和注意事项", "请列出{query}的具体方法和最佳实践"], "business_inquiry": ["请从商业角度分析{query}，包括市场机会和风险评估", "关于{query}的商业价值和投资回报，请提供专业分析", "请评估{query}的商业可行性和市场前景"], "technical_support": ["请为{query}提供技术解决方案和故障排除步骤", "关于{query}的技术问题，请提供详细的诊断和修复方法", "请分析{query}的技术原理和最佳实践"]}, "performance_settings": {"cache_enabled": true, "cache_ttl": 3600, "max_concurrent_requests": 100, "request_timeout": 30, "retry_attempts": 3, "batch_processing": true, "batch_size": 10}, "monitoring": {"enable_metrics": true, "enable_logging": true, "log_level": "INFO", "metrics_interval": 60, "alert_thresholds": {"error_rate": 0.05, "response_time": 5.0, "queue_size": 1000}}}